# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## Common Development Commands

### Build Commands
```bash
# Clean and rebuild the project
./gradlew clean
./gradlew assembleDebug

# Install debug APK on connected device
./gradlew installDebug

# Build release APK
./gradlew assembleRelease

# Run tests
./gradlew test
./gradlew connectedAndroidTest
```

### Development Setup Commands
```bash
# Check connected Android devices
adb devices

# Monitor app logs (replace with specific activity for focused logs)
adb logcat | grep "WorkingObjectDetection"
adb logcat | grep "FaceDetectionManager"
adb logcat | grep "ESP32CommunicationManager"

# Monitor system resources
adb shell dumpsys meminfo com.stemrobo.humanoid
adb shell top | grep com.stemrobo.humanoid

# Check camera availability
adb shell dumpsys media.camera
```

### ESP32 Development Commands
```bash
# Monitor ESP32 serial communication (adjust COM port for Windows)
# Use Arduino IDE Serial Monitor or similar tool for ESP32 development
# Default baud rate: 115200
```

## Architecture Overview

### System Components
This is a **tri-component robotics system**:

1. **Android App** (`com.stemrobo.humanoid`) - Primary UI and control center
2. **ESP32 Firmware** (`esp32-robot-controller-clean.ino`) - Hardware controller
3. **YOLO Object Detection** - Machine learning for computer vision

### Communication Architecture
- **USB Serial**: Primary communication mode (ESP32 ↔ Android)
- **WiFi Fallback**: Access Point mode ("ROBO SOCCER" network)
- **Local Databases**: Room (PersonDatabase) + SQLite (PresetDatabase)

### Core Android Package Structure
```
com.stemrobo.humanoid/
├── activities/          # Screen controllers (MainActivity, WorkingObjectDetectionActivity)
├── ai/                 # Gemini AI integration and conversation management
├── behaviors/          # Robot behavior logic (SmartGreetingManager, BehaviorManager)
├── communication/      # ESP32 communication layer (USB/WiFi)
├── database/           # Room + SQLite data persistence
├── fragments/          # UI fragment components
├── vision/             # Computer vision (face detection, YOLO, expression analysis)
├── services/           # Background services (camera, voice, heart rate)
├── models/             # Data structures and DTOs
└── utils/              # Helper utilities
```

### Key Activity Flow
- **MainActivity**: Main navigation hub with bottom navigation
- **WorkingObjectDetectionActivity**: Advanced computer vision with face detection, expression analysis, and YOLO integration
- **ObjectDetectionActivity**: Legacy object detection (reference implementation)

## Development Context

### Face Detection & Computer Vision
- Uses **ML Kit Face Detection** for real-time face recognition
- **Expression Analysis**: Detects Happy, Sad, Neutral, Surprised, Sleepy states
- **YOLO Integration**: Object detection with TensorFlow Lite
- **Real-time Performance**: <100ms face detection, <200ms expression analysis
- **Face Count Display**: Top-left corner real-time counter
- **Closest Face Focus**: Expression analysis targets nearest face only

### ESP32 Robot Hardware
- **Mecanum Wheel System**: 4-motor omnidirectional movement
- **Servo Control**: Left/right arms (0°-180°), head pan/tilt (0°-180°)
- **Ultrasonic Sensor**: Distance measurement for Smart Greeting (30cm handshake threshold)
- **Communication Modes**: USB UART (primary), WiFi Access Point (fallback)
- **Default WiFi**: SSID "ROBO SOCCER", Password "123456789"

### Database Architecture
Two separate databases:
- **PersonDatabase** (Room): Person recognition, embeddings, chat history
- **PresetDatabase** (SQLite): Robot action sequences and preset management

### AI Integration
- **Gemini AI**: Conversational AI with context memory
- **Voice Recognition**: Built-in Android speech services
- **Smart Greeting**: Automated human interaction with face distance detection
- **Real-time Data Service**: Continuous sensor monitoring

## Testing & Debugging

### Face Detection Testing
```bash
# Monitor face detection performance
adb logcat | grep -E "(FaceDetection|Expression|WorkingObject)"

# Test specific scenarios in WorkingObjectDetectionActivity:
# 1. Single face detection and expression analysis
# 2. Multiple faces with closest face targeting
# 3. Real-time face count updates
# 4. Various lighting conditions
```

### ESP32 Communication Testing
```bash
# Test USB serial connection
adb logcat | grep "ESP32CommunicationManager"

# Verify robot commands are being sent:
# - Motor controls (forward, backward, left, right, stop)
# - Servo movements (arms, head positioning)
# - Distance sensor readings for Smart Greeting
```

### Performance Benchmarks
- **Face Detection Latency**: < 100ms target
- **Expression Analysis**: < 200ms target
- **Memory Usage**: < 150MB target
- **Camera FPS**: 20+ FPS smooth preview

## Important Dependencies

### Core ML/Vision Libraries
```gradle
implementation 'com.google.mlkit:face-detection:16.1.6'
implementation 'androidx.camera:camera-core:1.3.1'
implementation 'org.tensorflow:tensorflow-lite:2.14.0'
implementation 'com.google.mediapipe:tasks-vision:0.10.8'
```

### Hardware Communication
```gradle
implementation 'com.github.mik3y:usb-serial-for-android:3.8.0'
```

### Database & AI
```gradle
implementation 'androidx.room:room-runtime:2.6.1'
// Gemini AI will be added in Phase 3
```

## Development Notes

### Hardware Integration Workflow
1. **Test ESP32 connectivity** before Android development
2. **USB Serial** is primary - WiFi is fallback only
3. **Distance streaming** runs at 200ms intervals for Smart Greeting
4. **Servo movements** use configurable speed (15ms default delay)

### Vision System Workflow
1. **Camera permissions** must be granted for vision features
2. **Face detection** runs continuously in WorkingObjectDetectionActivity
3. **Expression analysis** only on closest/largest face for performance
4. **YOLO object detection** can run simultaneously with face detection

### Database Migration Strategy
- **PersonDatabase**: Use Room migrations for schema changes
- **PresetDatabase**: Manual SQLiteOpenHelper.onUpgrade() implementation
- Always increment version numbers and test migrations thoroughly

### Performance Optimization
- **Vision processing** limited to closest face only
- **Distance streaming** optimized for 200ms intervals
- **Memory management** with proper cleanup in camera lifecycle
- **Background services** for non-blocking operations

## Common Issues & Solutions

### Face Detection Issues
- Ensure **good lighting** and **2-6 feet distance**
- Check **Google Play Services** installation for ML Kit
- Verify **camera permissions** are granted at runtime

### ESP32 Communication Issues
- **USB connection** takes priority over WiFi
- Check **serial baud rate** (115200) matches Android app
- **WiFi fallback** uses Access Point mode, not Station mode

### Build Issues
- Ensure **Java 17** compatibility in build.gradle
- **ML Kit dependencies** require `compileOptions` configuration
- **TensorFlow Lite** models need `aaptOptions { noCompress "tflite" }`

This robotics system combines advanced computer vision, AI conversation, and precise hardware control for autonomous humanoid robot operation.
